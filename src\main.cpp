#define TINY_GSM_MODEM_SIM800

#define SerialMon Serial

#include <SoftwareSerial.h>
SoftwareSerial SerialAT(16, 17);  // RX, TX


#include <TinyGsmClient.h>

#include <StreamDebugger.h>
StreamDebugger debugger(SerialAT, SerialMon);
TinyGsm        modem(debugger);

TinyGsmClient  client(modem);

void setup() {
  SerialMon.begin(115200);
  delay(10);

  SerialMon.println("Wait...");

  SerialAT.begin(115200);
  delay(6000);

  // Restart takes quite some time
  // To skip it, call init() instead of restart()
  SerialMon.println("Initializing modem...");
  // modem.restart(); init()
  modem.restart();

  String modemInfo = modem.getModemInfo();
  SerialMon.print("Modem Info: ");
  SerialMon.println(modemInfo);

}

void loop() {
   delay(2000);
  
  if (!modem.waitForNetwork()) {
    SerialMon.println(" fail");
    delay(10000);
    return;
  }
  SerialMon.println(" success");

  if (modem.isNetworkConnected()) { SerialMon.println("Network connected"); }

}