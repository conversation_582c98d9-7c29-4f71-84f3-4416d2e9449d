; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino
lib_deps = 
	plerup/EspSoftwareSerial@^8.2.0
	iakop/LiquidCrystal_I2C_ESP32@^1.1.6
	vshymanskyy/TinyGSM@^0.12.0
	vshymanskyy/StreamDebugger@^1.0.1
monitor_speed = 115200
